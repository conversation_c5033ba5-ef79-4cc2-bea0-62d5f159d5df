import docx
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

def create_ut_summary_document():
    # Create a new Document
    doc = docx.Document()

    # Set margins for the document
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)

    # Add title
    title = doc.add_heading('UT Team Status Update - Summary', level=0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Current Journey & Achievements
    doc.add_heading('Current Journey & Achievements', level=1)

    p = doc.add_paragraph()
    p.add_run('Hey team! <PERSON> shared some great insights about our journey so far. Building a Substrate extension for R9 has definitely been quite the adventure!')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('When we started, we were pretty much learning everything from scratch - both Substrate and R9 were new territories for us. Plus, both platforms have been evolving rapidly, which kept us on our toes. But here\'s the cool part: through all the build issues and live site incidents we\'ve tackled, we\'ve actually gained some solid expertise!')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('We\'ve figured out what a comprehensive package upgrade and R9 adoption solution for Substrate should look like. Along the way, we\'ve discovered multiple corner cases and developed good mitigations for them. We\'ve also gotten to know the Substrate community better and understand the different types of services - both DI and non-DI frameworks, including .NET Core.')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('It\'s exciting to see how the ecosystem is modernizing! Services are gradually switching from current APIs to v-next, which is a positive trend.')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('Our architecture has evolved too - we\'ve upgraded from what we called a "passive monitoring solution" (version 1.5 wrapper) to a more flexible and extensible solution. Now we have an approved approach that can smooth the migration process for any Substrate service we\'re familiar with.')

    # Next Semester Priorities
    doc.add_heading('Next Semester Priorities', level=1)

    p = doc.add_paragraph()
    p.add_run('Kyle outlined some key investments for the upcoming semester, organized into clear categories:')

    # Non-Negotiable Work
    doc.add_heading('1. Non-Negotiable Work (SFI)', level=2)
    p = doc.add_paragraph()
    p.add_run('This is our must-do work that we absolutely need to complete.')

    # Fundamental Solution Development
    doc.add_heading('2. Fundamental Solution Development', level=2)
    p = doc.add_paragraph()
    p.add_run('This is about getting our solution ready for the big migration:')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Log and Metric Migration: ').bold = True
    p.add_run('Moving from passive monitoring, IFX, and Substrate SDK to our new R9 extension')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Comprehensive Documentation: ').bold = True
    p.add_run('We need to create docs that answer all the questions customers might have, plus document all the issues we\'ve solved and provide clear solutions and to-do lists')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Efficiency Improvements: ').bold = True
    p.add_run('Based on recent leadership feedback, we need to speed things up. While cost savings are mission-critical, a three-week migration timeline is still too long for most services')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('To tackle the efficiency challenge, we\'ll need to collaborate closely with Chronicle and possibly OpsGenie to bring AI tools into the mix. These tools should help accelerate both R9 version upgrades and the migration process itself.')

    # Expanding Impact
    doc.add_heading('3. Expanding Impact & New Scenarios', level=2)
    p = doc.add_paragraph()
    p.add_run('With our basic solution about 80% ready (at least for passive monitoring), it\'s time to think bigger!')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('The most important and urgent expansion is the ')
    run = p.add_run('cost saving initiative')
    run.bold = True
    p.add_run(', which includes:')

    p = doc.add_paragraph()
    p.add_run('• Crystal dumper pipeline (starting point)\n')
    p.add_run('• Log sampling\n')
    p.add_run('• Cold storage\n')
    p.add_run('• Metric local aggregation')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('All these solutions are cost-targeted and should be built on top of R9.')

    # Vision Going Forward
    doc.add_heading('Our Vision Going Forward', level=1)

    p = doc.add_paragraph()
    p.add_run('Kyle\'s vision for the team is pretty ambitious and exciting: ')
    run = p.add_run('We want to become the go-to team for any service dealing with telemetry issues')
    run.bold = True
    p.add_run(' - whether they\'re:')

    p = doc.add_paragraph()
    p.add_run('• Greenfield or brownfield services\n')
    p.add_run('• Cosmos or data center services')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('This could involve helping with sampling, signal coverage, automation, and more!')

    # Key Takeaways
    doc.add_heading('Key Takeaways', level=1)

    p = doc.add_paragraph()
    p.add_run('• We\'ve come a long way from knowing nothing about Substrate and R9 to being experts\n')
    p.add_run('• Our solution is maturing and becoming more flexible\n')
    p.add_run('• We need to focus on speed and efficiency improvements\n')
    p.add_run('• Cost savings initiatives are our next big opportunity\n')
    p.add_run('• Chronicle collaboration will be key for AI-powered acceleration\n')
    p.add_run('• We\'re positioning ourselves as the telemetry experts for all services')

    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run('Great work everyone, and exciting times ahead! 🚀').bold = True

    # Save the document
    doc.save('UT_Team_Summary.docx')
    print("Document 'UT_Team_Summary.docx' has been created successfully.")

if __name__ == "__main__":
    create_ut_summary_document()
