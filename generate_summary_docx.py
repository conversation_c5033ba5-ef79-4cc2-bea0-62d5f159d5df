import docx
from docx.shared import P<PERSON>, <PERSON><PERSON>, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

def create_summary_document():
    # Create a new Document
    doc = docx.Document()

    # Set margins for the document
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)

    # Add title
    title = doc.add_heading('ODL PII Scrubber/Detector and EOP Remediation Plan', level=0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 1. Description of ODL PII Scrubber and Detector
    doc.add_heading('1. Description of ODL PII Scrubber and Detector', level=1)

    p = doc.add_paragraph()
    p.add_run('ODL (Office Data Loader) has two key components for handling sensitive data:').bold = True

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('EUII Scrubber: ').bold = True
    p.add_run('Responsible for hashing or encrypting EUII (End User Identifiable Information) before uploading to data storage. This component was created as the O365 Trust Approved solution for protecting sensitive data.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('EUII Detector: ').bold = True
    p.add_run('Responsible for detecting un-redacted sensitive data before uploading due to incorrect scrubbing rules, providing an additional layer of protection.')

    p = doc.add_paragraph()
    p.add_run('The ODL PII Scrubber is a critical component that:')

    p = doc.add_paragraph()
    p.add_run('• Works together with the uploading agent\n')
    p.add_run('• Is considered part of ODL\'s product highlight as a Trust-approved scrubber\n')
    p.add_run('• Provides essential compliance functionality for handling sensitive data')

    # 2. Current Usage of Certs and Secrets
    doc.add_heading('2. Current Usage of Certs and Secrets for EXO, EOP and Other Workloads', level=1)

    p = doc.add_paragraph()
    p.add_run('Current Secret Management Structure:').bold = True

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('HMAC Certificate (H100055): ').bold = True
    p.add_run('Used for one-way hashing of EUII data. Due to historical reasons, this certificate is shared across:')

    p = doc.add_paragraph()
    p.add_run('  - Different clouds: Public, DoD, GCC-H, and GCC clouds\n')
    p.add_run('  - Different workloads: Exchange, EOP, and other services\n')
    p.add_run('  - Purpose: Allows the same EUII data to be hashed into the same value, enabling downstream pipelines to join data using the hashed values')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Encryption Key (E100037): ').bold = True
    p.add_run('Used specifically in ITAR environments. Not shared across clouds but still requires rotation according to security policies.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Current Management: ').bold = True
    p.add_run('ODL team maintains EXO secrets in Azure Key Vault (AKV), which they currently manage. The team has developed an automation workflow (approved by compliance) that only needs the secret name in AKV to rotate and automate secret operations.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Data Flow: ').bold = True
    p.add_run('Exchange collects telemetry data (email communications metadata - who sends email to whom, when, from which IP address) using ODL. This data is EUII and is needed primarily by EOP for anti-spam services, not by Exchange directly.')

    # 3. List Issues
    doc.add_heading('3. Current Issues and Compliance Concerns', level=1)

    p = doc.add_paragraph()
    p.add_run('Security and Compliance Issues:').bold = True

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Compromised Secrets: ').bold = True
    p.add_run('The shared HMAC certificate (H100055) was found compromised in May last year and expired in January. It remains in use despite these serious security concerns.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Rotation Policy Violation: ').bold = True
    p.add_run('Security policy requires rotating encryption secrets every 90 days. The encryption key for ITAR clouds (E100037) was issued in June 2023 and has not been rotated according to policy.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Cross-Cloud Sharing: ').bold = True
    p.add_run('Sharing secrets across different clouds (Public, DoD, GCC-H) violates security best practices and creates vulnerability to insider attacks.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('USDP Policy Conflicts: ').bold = True
    p.add_run('Recent USDP (US Data Protection) policy changes make it impossible for the China-based ODL team to continue managing these secrets.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Compliance Risk: ').bold = True
    p.add_run('The current situation makes Exchange non-compliant since sensitive data is collected from Exchange servers using ODL. Risk Management has informed that M365 will not be allowed in ITAR clouds if there is any use of shared secrets by January 1, 2026.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('FedRAMP Compliance Risk: ').bold = True
    p.add_run('Non-remediation will jeopardize FedRAMP compliance and all M365 sales to the US Federal Government.')

    p = doc.add_paragraph()
    p.add_run('• ').bold = True
    p.add_run('Ownership Ambiguity: ').bold = True
    p.add_run('There is confusion about who should own the operational aspects of rotating secrets. EOP has indicated they are not resourced for that responsibility.')

    # 4. Mitigation Plan
    doc.add_heading('4. Mitigation Plan', level=1)

    p = doc.add_paragraph()
    p.add_run('Short-term Mitigation:').bold = True

    p = doc.add_paragraph()
    p.add_run('• ODL team has been working with Trust/EOP team to enable EUII dual-hashing with the new shared certificate (H100066)\n')
    p.add_run('• Currently waiting for EOP to confirm whether dual-hashing can be removed')

    p = doc.add_paragraph()
    p.add_run('Long-term Remediation Agreement:').bold = True

    p = doc.add_paragraph()
    p.add_run('• EOP is committing to end the use of shared secrets for EUII collected by Exchange on behalf of EOP this calendar year (2025)\n')
    p.add_run('• The solution must be in production and working before the 2025 Thanksgiving lockdown\n')
    p.add_run('• Contingency: If the remediation plan is not executed, AntiSpam services will no longer be available for ITAR users (DoD, GCC-H, GCC clouds)')

    p = doc.add_paragraph()
    p.add_run('Ownership Transition:').bold = True

    p = doc.add_paragraph()
    p.add_run('• Proposal to move ODL detector\'s ownership to Sukanya and Chris\'s organization\n')
    p.add_run('• Could be implemented as a complete transition or co-ownership model\n')
    p.add_run('• Recommendation to keep the scrubber in ODL for product integrity and sustainability reasons')

    # 5. Actions for ODL Team
    doc.add_heading('5. Actions for ODL Team', level=1)

    p = doc.add_paragraph()
    p.add_run('Current Limitations:').bold = True

    p = doc.add_paragraph()
    p.add_run('• Due to USDP changes, the ODL team (based in China) is unable to manage secrets anymore\n')
    p.add_run('• Need for a remote team member located in the US to help manage the AKV')

    p = doc.add_paragraph()
    p.add_run('Recommended Actions:').bold = True

    p = doc.add_paragraph()
    p.add_run('• Transfer ownership of EUII Scrubber/Detector operations to Sukanya Vaikuntapathy and Chris Rupp from EC org\n')
    p.add_run('• Continue developing automation tools that only need the secret name in AKV to rotate and automate secret operations\n')
    p.add_run('• Work toward the ideal "eyes-off" model where ODL doesn\'t manage any secrets in AKV but builds tools to consume data in a compliant way\n')
    p.add_run('• Participate in discussions to clarify ownership boundaries and responsibilities')

    # 6. Questions for Fritz
    doc.add_heading('6. Questions for Fritz', level=1)

    p = doc.add_paragraph()
    p.add_run('Key Questions:').bold = True

    p = doc.add_paragraph()
    p.add_run('1. Who in Substrate should own the EUII telemetry data that Exchange collects?\n\n')
    p.add_run('2. Can you help identify a Substrate security champion who can be involved in addressing these compliance concerns?\n\n')
    p.add_run('3. Do you agree with the proposed ownership transition plan for the ODL detector while keeping the scrubber in ODL?\n\n')
    p.add_run('4. Can you help facilitate a meeting to discuss the operational aspects of rotating secrets and clarify ownership responsibilities?\n\n')
    p.add_run('5. Are you aware of the SEV2 ICM (Incident-607963122) tracking the use of compromised secrets for data collected by Substrate?')

    # Save the document
    doc.save('Summary_for_Fritz_Updated.docx')
    print("Document 'Summary_for_Fritz_Updated.docx' has been created successfully.")

if __name__ == "__main__":
    create_summary_document()
