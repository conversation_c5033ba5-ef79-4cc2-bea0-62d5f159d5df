# UT Team Status Update - Summary

## Current Journey & Achievements

Hey team! <PERSON> shared some great insights about our journey so far. Building a Substrate extension for R9 has definitely been quite the adventure! 

When we started, we were pretty much learning everything from scratch - both Substrate and R9 were new territories for us. Plus, both platforms have been evolving rapidly, which kept us on our toes. But here's the cool part: through all the build issues and live site incidents we've tackled, we've actually gained some solid expertise!

We've figured out what a comprehensive package upgrade and R9 adoption solution for Substrate should look like. Along the way, we've discovered multiple corner cases and developed good mitigations for them. We've also gotten to know the Substrate community better and understand the different types of services - both DI and non-DI frameworks, including .NET Core.

It's exciting to see how the ecosystem is modernizing! Services are gradually switching from current APIs to v-next, which is a positive trend.

Our architecture has evolved too - we've upgraded from what we called a "passive monitoring solution" (version 1.5 wrapper) to a more flexible and extensible solution. Now we have an approved approach that can smooth the migration process for any Substrate service we're familiar with.

## Next Semester Priorities

<PERSON> outlined some key investments for the upcoming semester, organized into clear categories:

### 1. Non-Negotiable Work (SFI)
This is our must-do work that we absolutely need to complete.

### 2. Fundamental Solution Development
This is about getting our solution ready for the big migration:
- **Log and Metric Migration**: Moving from passive monitoring, IFX, and Substrate SDK to our new R9 extension
- **Comprehensive Documentation**: We need to create docs that answer all the questions customers might have, plus document all the issues we've solved and provide clear solutions and to-do lists
- **Efficiency Improvements**: Based on recent leadership feedback, we need to speed things up. While cost savings are mission-critical, a three-week migration timeline is still too long for most services

To tackle the efficiency challenge, we'll need to collaborate closely with Chronicle and possibly OpsGenie to bring AI tools into the mix. These tools should help accelerate both R9 version upgrades and the migration process itself.

### 3. Expanding Impact & New Scenarios
With our basic solution about 80% ready (at least for passive monitoring), it's time to think bigger! 

The most important and urgent expansion is the **cost saving initiative**, which includes:
- Crystal dumper pipeline (starting point)
- Log sampling
- Cold storage 
- Metric local aggregation

All these solutions are cost-targeted and should be built on top of R9.

## Our Vision Going Forward

Kyle's vision for the team is pretty ambitious and exciting: **We want to become the go-to team for any service dealing with telemetry issues** - whether they're:
- Greenfield or brownfield services
- Cosmos or data center services

This could involve helping with sampling, signal coverage, automation, and more!

## Key Takeaways
- We've come a long way from knowing nothing about Substrate and R9 to being experts
- Our solution is maturing and becoming more flexible
- We need to focus on speed and efficiency improvements
- Cost savings initiatives are our next big opportunity
- Chronicle collaboration will be key for AI-powered acceleration
- We're positioning ourselves as the telemetry experts for all services

Great work everyone, and exciting times ahead! 🚀
