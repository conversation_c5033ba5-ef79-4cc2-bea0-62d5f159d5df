Remediation plan for EOP use of shared cross cloud (including <PERSON><PERSON>, GCC-H) secrets for protecting EUII data

<PERSON> (M365)
​
<PERSON>;
​
<PERSON> (M365)
​


Regards!
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
<PERSON> | M365 - Substrate Foundation - OIC (UT, DT, ODL, EDS)

From: <PERSON><PERSON><PERSON> (AIM Consulting Addison Group) <<EMAIL>>
Sent: Wednesday, April 23, 2025 11:54 AM
To: <PERSON>-<PERSON>hr <<PERSON>.<PERSON>@microsoft.com>; <PERSON> (M365) <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; <PERSON> <<EMAIL>>; Sukanya Vaikuntapathy <su<PERSON><EMAIL>>; Chris Rupp <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; <PERSON> (M365) <<EMAIL>>; <PERSON> <PERSON> (M365) <<EMAIL>>
Subject: RE: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data
 
Many thanks for the willingness to help 😊

We are all trying to do the right thing here and we appreciate your support.

 

I will setup the meeting to discuss.

 

From: Fritz Darden-Behr <<EMAIL>>
Sent: Tuesday, April 22, 2025 5:29 PM
To: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>; Vincent Xu (M365) <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Allison Jones <<EMAIL>>; Sukanya Vaikuntapathy <<EMAIL>>; Chris Rupp <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Leo Zhang (M365) <<EMAIL>>
Subject: Re: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data

 

I'd love a meeting to understand this better.  I can help route / ensure we get the right owners.  @Iulian, there's not one owner for this — different teams own different logs and should be responsible for them.

 

-Fritz

From: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>
Sent: Tuesday, April 22, 2025 3:17 PM
To: Fritz Darden-Behr <<EMAIL>>; Vincent Xu (M365) <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Allison Jones <<EMAIL>>; Sukanya Vaikuntapathy <<EMAIL>>; Chris Rupp <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Leo Zhang (M365) <<EMAIL>>
Subject: RE: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data

 

Hey @Fritz, we are compliance and we are not aware of all the organizational intricacies.

 

Of the issues that Vincent is listing below, the only ones that need attention are #3 and #4. It is our understanding that ODL, like EOP are part of Substrate.

 

I am bubbling up those here for ease of reading:

Issue #3, 

 

There is another encryption key in ITAR (E100037) which is not shared across cloud, but it is also needs to be renewed due to some security policy. @Iulian Calinov (AIM Consulting Addison Group) wants ODL to help on it. But due to the recent USDP changes, ODL won't be able to help on it anymore. I think you should work with @Sukanya Vaikuntapathy, @Chris Rupp to figure out the solution, since ODL is not responsible for managing all these EUII Scrubbing / Detection things any more, and we are based in China. 

 

Issue #4,

 

In the attached thread, you mentioned some streams inside COSMOS that contain the sensitive data. I think COSMOS cannot store senstive data by design, and they will be scanned by PrivacyTrout. 

 

@Iulian Calinov (AIM Consulting Addison Group), if you believe the data inside COSMOS is senstive that violating USDP, you should work with @Sukanya Vaikuntapathy, @Chris Rupp and data owner to remove them.

 

ODL team doesn't own any data inside COSMOS, and COSMOS is by design to be accessible for everyone.

 

 

Substrate owns the EUII telemetry data. We (Compliance) understand ODL does not own the data, but Substrate does, and I was under the impression that you @Fritz represent Substrate in this discussion, sorry if I misunderstood.

We asked EOP today who should own the operational aspects of rotating secrets and they indicated that they are not resourced for that.

 

So maybe @Fritz, the question for you is who in Substrate owns EUII telemetry data that Exchange collects? Every other team in M365 takes ownership of the operational needs to manage secrets.

Can you help take this on? Compliance cannot make that sort of a decision ☹

 

More than happy to call a meeting to clarify.

 

 

 

From: Fritz Darden-Behr <<EMAIL>>
Sent: Tuesday, April 22, 2025 2:59 PM
To: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>; Vincent Xu (M365) <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Allison Jones <<EMAIL>>; Sukanya Vaikuntapathy <<EMAIL>>; Chris Rupp <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Leo Zhang (M365) <<EMAIL>>
Subject: Re: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data

 

What are you looking for Scott, Allison, and I to respond to?  We represent ODL and it sounds from Vincent that the work here is primarily not on the ODL team.

 

-Fritz

From: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>
Sent: Tuesday, April 22, 2025 9:30 AM
To: Vincent Xu (M365) <<EMAIL>>; Fritz Darden-Behr <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Allison Jones <<EMAIL>>; Sukanya Vaikuntapathy <<EMAIL>>; Chris Rupp <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Leo Zhang (M365) <<EMAIL>>
Subject: RE: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data

 

Thank you for the clarity @Vincent. I am looking up to @SCOTT, @Allison and @Fritz to respond before setting up a meeting.

I will also coordinate offline with others on next steps.

 

Just for the record on #4. Per Security, encrypted EUII is still EUII. It might not fall into the leak detection program, but it is EUII and hence sensitive. And this is further complicated by the original clear text data being protected with expired and compromised secrets. Just stating facts, not laying any blame.

 

But all that is an integral part of the underlying issue, and I think the data owners (Exchange, EOP) might be the appropriate owners of remediation (not ODL), but that is something we will be working to figure out.

 

Will let you know how things evolve.

 

From: Vincent Xu (M365) <<EMAIL>>
Sent: Tuesday, April 22, 2025 12:17 AM
To: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>; Fritz Darden-Behr <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Allison Jones <<EMAIL>>; Sukanya Vaikuntapathy <<EMAIL>>; Chris Rupp <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Leo Zhang (M365) <<EMAIL>>
Subject: Re: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data
Importance: High

 

Lulian,

 

I think we do need a meeting to clarify the issues we are discussing here and the actions you are expecting the ODL team to do, since some of the work are actually out of ODL team's responsibilities. So, pls schedule some time for us ASAP.

 

Just try my best to clarify the things we are talking in this thread per my understanding.

 

Issue #1, 

 

Due to historical reason, a shared cross cloud HMAC cert is used by EUII Scrubber (a plugin in ODL) to scrub the sensitive data before uploading. The shared cross cloud cert (H100055) is something confirmed to be vulnerable to insider attacks and otherwise not meeting compliance requirements. We need to remove the usage of this shared cert, but unfortunately, EOP has feature (AntiSpam per the info in this thread) depends on this shared cert. As @Iulian Calinov (AIM Consulting Addison Group) mentioned in the beginning of the thread, looks like some agreement is done between EOP and Trust team. 

 



 

For this issue #1, I think @Iulian Calinov (AIM Consulting Addison Group) needs the ack from Exchange side to agree on the plan. No action from ODL team is needed.

 

Issue #2, 

 

The shared cross cloud cert (H100055) is known to be long expired and compromised. So Trust team wants to renew it as the mitigation. 

 

1.       For short-term mitigation, ODL team has been working with Trust/EOP team to enable EUII dual-hashing with the new shared cert (H100066) as well. Now ODL is waiting for EOP side to confirm whether we could remove the dual-hashing. 

 

2.       For long-term, ODL is not responsible for managing all these EUII Scrubbing / Detection things (they just plugins inside ODL). They should be owned by @Sukanya Vaikuntapathy, @Chris Rupp from EC org. @Iulian Calinov (AIM Consulting Addison Group), you need to work out the details with them.

 

Issue #3, 

 

There is another encryption key in ITAR (E100037) which is not shared across cloud, but it is also needs to be renewed due to some security policy. @Iulian Calinov (AIM Consulting Addison Group) wants ODL to help on it. But due to the recent USDP changes, ODL won't be able to help on it anymore. I think you should work with @Sukanya Vaikuntapathy, @Chris Rupp to figure out the solution, since ODL is not responsible for managing all these EUII Scrubbing / Detection things any more, and we are based in China. 

 

Issue #4,

 

In the attached thread, you mentioned some streams inside COSMOS that contain the sensitive data. I think COSMOS cannot store senstive data by design, and they will be scanned by PrivacyTrout. 

 

@Iulian Calinov (AIM Consulting Addison Group), if you believe the data inside COSMOS is senstive that violating USDP, you should work with @Sukanya Vaikuntapathy, @Chris Rupp and data owner to remove them.

 

ODL team doesn't own any data inside COSMOS, and COSMOS is by design to be accessible for everyone.

 

Regards!
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Vincent Xu | M365 - Substrate Foundation - OIC (UT, DT, ODL, EDS)

 

From: Iulian Calinov (AIM Consulting Addison Group)
Sent: Tuesday, April 22, 2025 1:44 AM
To: Fritz Darden-Behr; Vincent Xu (M365); SCOTT LABADIE; Allison Jones
Cc: Jane Tieu (MBD); Steve Zhang (M365); Chris Rupp
Subject: RE: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data

 

Thanks for chiming in @Fritz.

 

As you noted, this issue has a very long history (years) and has been made acute recently. In a nutshell, Exchange collects and stores sensitive EUII data which is improperly protected in Cosmos. EOP uses the data, but has been postponing fixing their architecture for many years. This appears to be no longer tenable due to regulatory changes, and Compliance would like to make sure there is adequate awareness from Substrate.

 

More than happy to setup a meeting to explain in more detail. Ideally someone from Substrate privacy, security participates.

 

 

 

From: Fritz Darden-Behr <<EMAIL>>
Sent: Monday, April 21, 2025 11:35 AM
To: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>; Vincent Xu (M365) <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Allison Jones <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Chris Rupp <<EMAIL>>
Subject: Re: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data

 

Long thread here, and I'm not sure I'm following.  @Vincent, who exactly needs to schedule work for this to be fixed?  Is there a way we can create a deadline to turn off the logging for something that isn't compliant if they don't do the work?

 

-Fritz

From: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>
Sent: Monday, April 21, 2025 10:38 AM
To: Vincent Xu (M365) <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Fritz Darden-Behr <<EMAIL>>; Allison Jones <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Chris Rupp <<EMAIL>>
Subject: RE: Remediation plan for EOP use of shared cross cloud (including DoD, GCC-H) secrets for protecting EUII data

 

@SCOTT, @Fritz, @Allison,

 

Gently bubbling this up to you. I am making thisemail  as high importance, since it looks like there are shifting regulatory requirements which makes it impossible for the current operational arrangement for managing Substrate secrets to continue.

 

Do you have a Substrate security champion who can be involved here? I am no expert, but I believe expired secrets are very sensitive, especially when certain sovereign customers are involved.

 

 

I believe expired encryption key ICMs might have relatively stringent requirements for response times and priorities when customers like DoD, GCC-H are involved. The initial priority for the ICM was lowered to 3 by ODL, but they since realized they cannot manage this ICM any longer.

 

You input here would be appreciated.

 

Thank you.

 

From: Iulian Calinov (AIM Consulting Addison Group)
Sent: Thursday, April 17, 2025 9:36 AM
To: Vincent Xu (M365) <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Fritz Darden-Behr <<EMAIL>>; Allison Jones <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>; Chris Rupp <<EMAIL>>
Subject: RE: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

Thank you for adding me to this thread @Vincent 😊

 

Adding Chris as FYI.

 

Scott, there is a lot of history here.

 

The TL;DR here is this:

Exchange collects EUII data EOP needs and is using non compliant (long expired and compromised) secrets to protect this data in Cosmos.

These issues are long standing, made more sensitive over the last year due to the SFI as well as USDP policy changes.

All other M365 services fixed similar issues long time ago, but EOP is taking longer.

 

The problem is that this situation makes Exchange non compliant, since data is collected from Exchange servers, using ODL, even though Exchange does not need any of this EUII.

 

Compliance is kindly asking confirmation that Exchange is aware and ok with the business risk to Exchange of this EUII data collection by EOP.

Compliance is working already with EOP on remediation, but it would be great if Exchange would also weigh into the discussion.

 

 

Details

 

Exchange is collecting telemetry data using ODL. This telemetry data is email communications metadata (who sends email to whom, when, from which IP address), which is EUII. It is my understanding that this data is needed by EOP for anti spam services, and not by Exchange directly. As per commitments to our customers, this EUII is scrubbed by ODL. Scrubbing for EOP means hashing with a secret salt and separately encrypting with a separate copy with a different secret.

 

Unfortunately, EOP has an obsolete design they keep postpone fixing, which requires sharing the hashing secrets across different clouds. In this case secrets are shared between Public and all ITAR clouds (not AG though). EOP usually takes a long time to rotate secrets. So, they are currently using a secret (100055) that was found compromised in May last year and also expired back in January. Additionally, EOP is using ODL to encrypt a separate copy of the data. The secret used to encrypt this data for ITAR clouds (E100037) was issued in June 2023 and not rotated. The current security policy requires rotating such secrets every 90 days. They were last rotated by the ODL team, but that is no longer possible due to various policies. Also, encrypting EUII is considered still EUII by security and not EUPI.

 

Each of the above issues is problematic from a security perspective, taken together they create quite a headache ☹, which is made more difficult by the recent policy changes. Compliance is working with EOP to address, but remediation has been going on since 2020 at least and is generally slow because of resource constraints from EOP.

 

I can share with you various ICMs and Exceptions tracking the details of all issues above. Let me know if you have any questions.

 

 

From: Vincent Xu (M365) <<EMAIL>>
Sent: Wednesday, April 16, 2025 8:56 PM
To: SCOTT LABADIE <<EMAIL>>; Fritz Darden-Behr <<EMAIL>>; Allison Jones <<EMAIL>>; Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>; Steve Zhang (M365) <<EMAIL>>
Subject: Re: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

Scott/Fritz, 

 

Sorry for the delay response since somehow I missed this thread earlier.

 

I were sharing this information per the ask from Trust team. But I would like to let you know that I am in conversation with Trust team to clarify the right PoC that they should work with for this in the future, since ODL team should be no longer owning EUII Scrubber/Detector, including the related operation work, especially with the recent USDP policy. I will let you know once I locked down the plan with them.

 

For questions from @SCOTT LABADIE, let me give you a little bit more background here. :)

 

ODL has two component, 1) EUII Scrubber to hash or encrypt EUII before uploading; 2) EUII Detector to detect un-redacted data before uploading due to incorrect scrubbing rules. 
The EUII Scrubber was created long time ago as the O365 Trust Approved solution. The HMAC cert mentioned in this thread is for 1-way hashing.
Due to historical reason, this HMAC cert is shared across workloads and environments, so that the *same EUII* could be hashed in to the *same value*, then the downstream pipeline could still join the data with the "hashed" value. AFAIK, the sharing status is something like below diagram. 
 



 

Due to risk of the shared secrets, EOP wants to remove the sharing across clouds (green box). @Iulian Calinov (AIM Consulting Addison Group) from Trust to help to confirm whether they have plan to remove the sharing across workloads (orange box) as well.
Currently, there is no design needed from ODL side, since it already supports dual hashing during secret transition (As I mentioned in the beginning, this should not be owned by ODL any more, but privacy team. I am in conversation with them). The major work will be on EOP side to end the usage of the shared secrets. 
 

Regards!
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Vincent Xu | M365 - Substrate Foundation - OIC (UT, DT, ODL, EDS)

 

From: SCOTT LABADIE
Sent: Thursday, April 10, 2025 10:23 PM
To: Vincent Xu (M365); Fritz Darden-Behr; Allison Jones
Cc: Jane Tieu (MBD)
Subject: Re: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

Thanks Vincent, a couple follow-up questions ...

 

I went to the context on expectations below, "EOP is committing to end the use of shared secrets for EUII collected by Exchange on behalf of EOP this calendar year - 2025"

 

If this is for the ODL is it for the EUII detector and scrubber? If so, is it collecting data? I've seen it called a scrubber. Do we do collection? 

 

On the sharing, how broad is it? Is it the same secret for different rings? For different geos? For different service data?

 

To end the use of scared secrets, do we have a design/outline of what we're changing to meet the expectations here?

 

Thanks,

Scott

 

From: Vincent Xu (M365) <<EMAIL>>
Sent: Wednesday, April 9, 2025 9:25 PM
To: Fritz Darden-Behr <<EMAIL>>; SCOTT LABADIE <<EMAIL>>; Allison Jones <<EMAIL>>
Cc: Jane Tieu (MBD) <<EMAIL>>
Subject: Fw: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

@Fritz Darden-Behr, @SCOTT LABADIE, @Allison Jones

 

Forwarding this mail per the ask from Trust team. 

 



 

To give you more background, the shared secret is the shared HMAC cert 100055, which will be used by ODL and other services in EOP to scrub the EUII content and performed join in later data pipelines. 

 

Regards!
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Vincent Xu | M365 - Substrate Foundation - OIC (UT, DT, ODL, EDS)

 

From: Iulian Calinov (AIM Consulting Addison Group)
Sent: Saturday, March 29, 2025 2:11 AM
To: Steve Zhang (M365); Vincent Xu (M365)
Cc: Chris Rupp
Subject: RE: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

Thank you @Steve 😊

 

Please note that the ICM was downgraded by EOP to SEV3 and stated they target a 6 months timeframe for resolution. We are making sure these are reviewed by Security.

 

Considering the exposure and audit and business risk, Sukanya suggested Exchange management is aware of what is going on, since Exchange collects the data on behalf of EOP and is accountable for its protection at rest.

 

Let us know if you have any questions.

 

From: Steve Zhang (M365) <<EMAIL>>
Sent: Friday, March 28, 2025 3:55 AM
To: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>; Vincent Xu (M365) <<EMAIL>>
Cc: Chris Rupp <<EMAIL>>
Subject: RE: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

FYI, Vincent (Dev manager of ODL service) will help to present this information to Exchange Leadership.

Thanks,

Steve

From: Iulian Calinov (AIM Consulting Addison Group) <<EMAIL>>
Sent: Friday, March 21, 2025 3:31 AM
To: Steve Zhang (M365) <<EMAIL>>
Cc: Chris Rupp <<EMAIL>>
Subject: FW: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

Hey @Steve, as discussed, this is the current agreement we have with EOP on the use of the shared secrets. It will be used to have the exception of using shared secrets approved by Security most likely Monday.

 

Please notify the Substrate/Exchange leadership of:

Continued use of shared secrets to collect EUII data by Substrate
The SEV2 ICM linked below, for tracking the use of a compromised and expired secrets for data collected by Substrate
 

Thank you.

 

 

From: Iulian Calinov (AIM Consulting Addison Group)
Sent: Thursday, March 20, 2025 12:24 PM
To: Fergal Burke <<EMAIL>>; Paula Greve <<EMAIL>>; Holly Stewart <<EMAIL>>; Ciaran Murphy <<EMAIL>>; George Li <<EMAIL>>; Dana Cristofor <<EMAIL>>
Cc: Chris Rupp <<EMAIL>>; Frank Brisse <<EMAIL>>; Cesar Bonilla <<EMAIL>>; Christopher Maxwell Flores <<EMAIL>>
Subject: Remediation plan for EOP use of shared cross cloud secrets for protecting EUII data

 

The purpose this email is to disseminate the plan of record for the remediation plan for the use of cross cloud secrets by EOP in protecting of data at rest in out of boundary storage such as Cosmos and Kusto.

This data, collected from Public, DoD, GCC-H and GCC cloud users, is currently known to be vulnerable to insider attacks and otherwise not meeting compliance requirements.

 

High level agreement

EOP is committing to end the use of shared secrets for EUII collected by Exchange on behalf of EOP this calendar year - 2025
The solution must be in production and working before the 2025 Thanksgiving lockdown
As a contingency, EOP agrees that AntiSpam services will no longer be available for use by ITAR users (DoD, GCC-H, GCC clouds) if the remediation plan is not executed
 

We are stating that this agreement has been approved by EOP leadership (Fergal Burke), Risk Management and Compliance and is recommended for approval by Microsoft Security.

 

Why this needs to be done

Risk Management has informed Compliance and EOP that M365 will not be allowed in ITAR clouds if there is any use of shared secrets on Jan 1st 2026. Non remediation will jeopardize FedRAMP compliance and all M365 sales (for any and all services) to the US Federal Government.

 

Action required

EOP – we are assuming this plan has been approved by EOP leadership, please reach out if you disagree.

Exchange/Substrate – this plan is FYI for Substrate leadership.

 

This plan, as assumed approved, will be used by Risk Management and Microsoft Security to approve the exception tracking this issue.

 

Further information

All work under this remediation plan is tracked by this exception: https://o365trustcompliance.visualstudio.com/Trust/_workitems/edit/236521

 

Note: this email does not cover the unrelated issue of using compromised secrets by EOP. This is tracked by this SEV2 ICM: Incident-607963122 Details - IcM

 

 

 