UT common-20250526_194210-Meeting Recording
May 26, 2025, 11:42AM

<PERSON> started transcription

<PERSON>   0:53
As I already summarized in previous meeting, the path that Utah team took to build a substrate extension for R9 is not an easy one.
At the beginning of building this solution, we were lack of knowledge both of substrate and of R9. In the meantime, substrate itself is evolving and same to R9, so.
By fixing all of these build issues, the live site incidents, we gain knowledge of what could be a holistic package upgrade and R9 adoption solution in substrate could be and we figure out.
Multiple corner cases and figured out the mitigation to them. In the meantime we gain knowledge of the subject community, understand different types of services like DI and non DI like framework and.net core. We've also see that the community is evolving.
The ecosystem is evolving to be more modernized. We see services switching from gradually from the current API to vnext.
And we also went into get into conversations that question our standing point of what we are building the passive monitoring solution from a wrapper to we call it version 1.5 and to extension.
We gradually upgraded our architecture to be more flexible and extensible and finally we're at the point that we have approved solution to smooth the migration effort for any substrate service.
At least those that we know well, a similar thing is happening with.
Substrate SDK and IFX. So standing here, I want to summarize a few key investments we're planning in next semester. First, the first batch of work item I call it.
Unnegotiable, which is the SFI work.
And apart from that, another category is called the fundamental solution, meaning that we need to get our solution ready for log and metric migration from passive monitoring from IFX and from subset SDK.
To a new extension, a new integration with R9 in an extensible way.
We need to ensure that the solution contains necessary document to cover all of the questions that customer can went into and summarize all of the questions we've mitigated and.
Provide straightforward solution and to do list.
Another work item in under this category is to improve the efficiency based on the feedback we got from recent conversation with leadership. Although the cost saving is mission critical to the organization, but a three weeks migration effort is still too long for most of the services.
So we need to figure out a way to.
Accelerate the adoption process. This requires us to deeply collaborate with Chronicle and maybe OPS Genie, whatever team that can bring AI tools to help us upgrade both the R9 version upgrade.
Um and uh the the the and the migration itself, so the.
And that's the part of the basic solution. Apart from that, I think it's time for us with the basic solution. I say at least for passive monitoring, 80% ready, I would say that we need to think of harvesting more impact built on top of it.
Top this solution. This means we need to expand to more scenarios and among all of these scenarios, the most important one and urgent one is the Cox saving initiative. The Cox saving started from.
The crystal, the crystal dumper pipeline and will gradually roll out to log sampling, to cold storage and to metric local aggregation. All of these solutions are Cox targeted but should be based on top of R9 and when we see when we say R9 of substrate that means it's.
Our scope in general, I want us to, I want our team to be the go to team whenever every service have issue dealing with their telemetry regardless of if there are Greenfield or brownfield services.
And if they're cosmic or data center services, this could be sampling. This could be about sampling, it could be about the signal coverage, it could be about automatic.

Kyle Zhao stopped transcription
