Connect for Yanling-20250529_204704-Meeting Recording
May 29, 2025, 12:47PM

<PERSON> started transcription

<PERSON>   0:20
<PERSON>, I'm glad to see that you're back to majority of your bandwidth is switched back to UT team. I know there were still some leftover work from fleet that you need to continue support in this semester.
Luckily, that part of work is not was not your liability, but with the familiarity with fleet team and deployment team, we were able to fix the machine provisioning state issue, which is a legacy issue of passive monitoring.
I think that wouldn't be possible without your expertise and your experience, so good work. Apart from that, I hope you already find that situation is different.
In UT compared to the situation before you.
Move to help fleet team. We're no longer building the wrapper extension, but we still remain the initiative of moving substrate services smoothly to R9.
And we're trying to build our extension on top of R9 SDK and.
Following the same design principle with R9 main package to build our own extension on top of it and you contributed a lot to the metering migration solution.
And your maturity is not only revealed in the development of the extension itself, but also providing a practical and holistic migration guidance document and also.
Validate into the DSAPI SDK which is the real world heavily used library in substrate ecosystem and also your support to OWA on the R9 event. Onboarding is also provide you more practical experience understanding.
Customers and make the solution even more robust.

<PERSON> stopped transcription
